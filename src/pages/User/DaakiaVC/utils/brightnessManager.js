// Brightness manager utility - stateless functions like virtualBackground.js

// Simple state storage (no complex singleton pattern)
let participantBrightness = new Map();
let rpcRegistered = false;
let unregisterRpc = null;
let debounceTimer = null;
let currentRoom = null;

const handleRpcMessage = (data) => {
  try {
    const payload = JSON.parse(data.payload);
    const { brightness } = payload;
    console.log(`📥 [BRIGHTNESS] Received brightness ${brightness} from participant:`, data.callerIdentity);
    participantBrightness.set(data.callerIdentity, brightness);
    return "Brightness updated successfully";
  } catch (error) {
    console.error('❌ Error processing brightness RPC:', error);
    return "Error: Failed to update brightness";
  }
};

  const registerRpcMethod = () => {
    if (!state.room || state.rpcRegistered) {
      console.log('🔄 [BRIGHTNESS] RPC already registered or no room, skipping');
      return;
    }

    if (state.room.state !== "connected" || !state.room.localParticipant) {
      console.warn('⚠️ Cannot register brightness RPC: room not connected or local participant unavailable');
      return;
    }

    try {
      // Unregister existing RPC if any (defensive cleanup)
      if (state.unregisterRpc) {
        console.log('🔄 [BRIGHTNESS] Unregistering existing RPC method');
        try {
          state.unregisterRpc();
        } catch (cleanupError) {
          console.warn('⚠️ [BRIGHTNESS] Error during RPC cleanup:', cleanupError);
        }
        state.unregisterRpc = null;
        state.rpcRegistered = false;
      }

      // Check if method is already registered (additional safety)
      const existingMethods = state.room.localParticipant.rpcMethods || new Map();
      if (existingMethods.has && existingMethods.has("setBrightness")) {
        console.warn('⚠️ [BRIGHTNESS] setBrightness RPC method already exists, skipping registration');
        return;
      }

      state.unregisterRpc = state.room.localParticipant.registerRpcMethod(
        "setBrightness",
        handleRpcMessage
      );

      state.rpcRegistered = true;
      console.log('✅ [BRIGHTNESS] RPC method registered successfully');
    } catch (error) {
      console.error('❌ Failed to register brightness RPC method:', error);
      // Reset state on error
      state.rpcRegistered = false;
      state.unregisterRpc = null;
    }
  };

  const sendToParticipant = async (participant, brightness) => {
    try {
      if (!state.room || !state.room.localParticipant) {
        throw new Error('Room or local participant not available');
      }

      if (!participant || !participant.identity) {
        throw new Error('Participant identity is missing');
      }

      if (state.room.state !== "connected") {
        throw new Error(`Room not connected (state: ${state.room.state})`);
      }

      console.log(`📤 [BRIGHTNESS] Sending brightness ${brightness} to participant:`, participant.identity);

      const payload = JSON.stringify({
        brightness,
        participantId: state.room.localParticipant.identity,
        timestamp: Date.now()
      });

      const response = await state.room.localParticipant.performRpc({
        destinationIdentity: participant.identity,
        method: "setBrightness",
        payload,
        responseTimeout: 5000
      });

      console.log(`✅ [BRIGHTNESS] Successfully sent brightness to ${participant.identity}:`, response);
      return { participant: participant.identity, success: true, response };
    } catch (error) {
      console.error(`❌ [BRIGHTNESS] Failed to send brightness to ${participant?.identity || 'unknown'}:`, error.message);
      return { participant: participant?.identity || 'unknown', success: false, error: error.message };
    }
  };

  const createBatches = (array, batchSize) => {
    const batches = [];
    for (let i = 0; i < array.length; i += batchSize) {
      batches.push(array.slice(i, i + batchSize));
    }
    return batches;
  };

  const executeBrightnessSend = async (brightness, maxConcurrent) => {
    // Validate room and connection state
    if (!state.room || state.room.state !== "connected" || !state.room.localParticipant) {
      console.warn('⚠️ Cannot send brightness: room not connected or local participant unavailable');
      return;
    }

    if (brightness === 100) {
      console.log('🔆 [BRIGHTNESS] Brightness is 100, skipping send');
      return;
    }

    const remoteParticipants = Array.from(state.room.remoteParticipants.values());
    console.log(`📡 [BRIGHTNESS] Sending brightness ${brightness} to ${remoteParticipants.length} participants:`, remoteParticipants.map(p => p.identity));

    if (remoteParticipants.length === 0) {
      console.log('👥 [BRIGHTNESS] No remote participants to send brightness to');
      return;
    }
    const batches = createBatches(remoteParticipants, maxConcurrent);

    for (const batch of batches)  {
      const promises = batch.map(participant => sendToParticipant(participant, brightness));
      // eslint-disable-next-line no-await-in-loop
      await Promise.allSettled(promises);

      if (batches.length > 1) {
        // eslint-disable-next-line no-await-in-loop
        await new Promise(resolve => {
          setTimeout(resolve, 100);
        });
      }
    }
  };

  const cleanupDisconnectedParticipants = () => {
    if (!state.room) return;

    const connectedParticipants = new Set(
      Array.from(state.room.remoteParticipants.keys())
    );

    for (const participantId of state.participantBrightness.keys()) {
      if (!connectedParticipants.has(participantId)) {
        state.participantBrightness.delete(participantId);
      }
    }
  };

  const handleParticipantDisconnected = (participant) => {
    if (participant && participant.identity) {
      // Clean up brightness data for the disconnected REMOTE participant
      // Note: This only triggers for remote participants, not local participant
      state.participantBrightness.delete(participant.identity);
      console.log(`🧹 Cleaned up brightness data for disconnected participant: ${participant.identity}`);
    }
  };

  const handleLocalParticipantDisconnected = () => {
    console.log('🧹 Local participant disconnected - performing complete cleanup');
    // When WE (local participant) disconnect, clean up everything
    state.participantBrightness.clear();
  };

  const setupParticipantEventListeners = () => {
    if (!state.room) return;

    // Clean up when REMOTE participants disconnect
    state.room.on('participantDisconnected', handleParticipantDisconnected);

    state.room.on('disconnected', handleLocalParticipantDisconnected);

    if (!state.cleanupTimer) {
      state.cleanupTimer = setInterval(() => {
        cleanupDisconnectedParticipants();
      }, 300000); // Every 5 minutes as fallback
    }
  };

  const removeParticipantEventListeners = () => {
    if (state.room) {
      state.room.off('participantDisconnected', handleParticipantDisconnected);
      state.room.off('disconnected', handleLocalParticipantDisconnected);
    }
  };


  return {
    get participantBrightness() { return state.participantBrightness; },
    get rpcRegistered() { return state.rpcRegistered; },
    get room() { return state.room; },
    get currentBrightness() { return state.currentBrightness; },

    initialize(room) {
      if (!room) return;

      // Prevent double initialization - check both room object and connection state
      if (state.room === room && state.rpcRegistered) {
        console.log('🔄 [BRIGHTNESS] Already initialized for this room, skipping');
        return;
      }

      // Additional safety check - if we have a different room, cleanup first
      if (state.room && state.room !== room) {
        console.log('🔄 [BRIGHTNESS] Different room detected, cleaning up previous initialization');
        this.cleanup();
      }

      // Only initialize if room is connected
      if (room.state !== 'connected') {
        console.warn('⚠️ [BRIGHTNESS] Cannot initialize: room not connected (state:', room.state, ')');
        return;
      }

      // Extra protection: check if we're already managing this exact room
      if (state.room === room && state.room.state === 'connected') {
        console.log('🔄 [BRIGHTNESS] Room already managed and connected, re-registering RPC only');
        registerRpcMethod();
        return;
      }

      console.log('🔧 [BRIGHTNESS] Initializing brightness manager for room:', room.name);
      state.room = room;
      registerRpcMethod();
      setupParticipantEventListeners();
    },

    sendBrightnessToAll(brightness, options = {}) {
      const {
        debounceMs = 1000,
        skipIfSame = true,
        maxConcurrent = 10
      } = options;

      if (state.debounceTimer) {
        clearTimeout(state.debounceTimer);
      }

      if (skipIfSame && state.currentBrightness === brightness) {
        return;
      }

      state.currentBrightness = brightness;

      state.debounceTimer = setTimeout(() => {
        console.log(`⏰ [BRIGHTNESS] Debounce timer fired, executing brightness send: ${brightness}`);
        executeBrightnessSend(brightness, maxConcurrent);
      }, debounceMs);
    },

    sendToNewParticipant(participant, brightness = state.currentBrightness) {
      // Validate inputs
      if (!participant || !participant.identity) {
        console.warn('⚠️ Cannot send brightness: invalid participant');
        return;
      }

      if (brightness === 100) {
        return;
      }

      if (!state.room || state.room.state !== "connected" || !state.room.localParticipant) {
        console.warn(`⚠️ Cannot send brightness to ${participant.identity}: room not ready`);
        return;
      }

      setTimeout(() => {
        if (state.room && state.room.state === "connected" && state.room.localParticipant) {
          sendToParticipant(participant, brightness);
        } else {
          console.warn(`⚠️ Room state changed, skipping brightness send to ${participant.identity}`);
        }
      }, 3000);
    },

    getParticipantBrightness(participantId) {
      return state.participantBrightness.get(participantId) || 100;
    },

    getAllParticipantBrightness() {
      return new Map(state.participantBrightness);
    },

    cleanup() {
      // Clear timers
      if (state.debounceTimer) {
        clearTimeout(state.debounceTimer);
        state.debounceTimer = null;
      }

      if (state.cleanupTimer) {
        clearInterval(state.cleanupTimer);
        state.cleanupTimer = null;
      }

      removeParticipantEventListeners();

      if (state.unregisterRpc && state.rpcRegistered) {
        try {
          state.unregisterRpc();
        } catch (error) {
          console.error('❌ Error unregistering brightness RPC method:', error);
        }
      }

      state.participantBrightness.clear();
      state.rpcRegistered = false;
      state.unregisterRpc = null;
      state.room = null;
      state.currentBrightness = 100;
    },

    handleRpcMessage,
    registerRpcMethod
  };
};

const brightnessManager = createBrightnessManager();

// Optimized brightness manager functions for direct use
export const initializeBrightnessManager = (room) => {
  if (room) {
    brightnessManager.initialize(room);
  }
};

export const sendBrightnessToAll = (brightness, options = {}) => {
  console.log(`🎯 [BRIGHTNESS] sendBrightnessToAll called with brightness: ${brightness}`);
  brightnessManager.sendBrightnessToAll(brightness, {
    debounceMs: 100, // Reduced debounce for faster response
    skipIfSame: true,
    maxConcurrent: 10,
    ...options
  });
};

export const sendBrightnessToNewParticipant = (participant, brightness) => {
  brightnessManager.sendToNewParticipant(participant, brightness);
};

export const getParticipantBrightness = (participantId) => {
  return brightnessManager.getParticipantBrightness(participantId);
};

export const getAllParticipantBrightness = () => {
  return brightnessManager.getAllParticipantBrightness();
};

export const cleanupBrightnessManager = () => {
  brightnessManager.cleanup();
};

export default brightnessManager;
